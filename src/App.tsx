import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import "./index.css";

export function App() {
  // Buy calculation states
  const [percentage, setPercentage] = useState<string>("");
  const [poolBnb, setPoolBnb] = useState<string>("0.06");
  const [k, setK] = useState<string>("0.0000000163963");
  const [totalSupply] = useState<number>(1000000000); // 10亿总供应量
  const [buyResults, setBuyResults] = useState<{
    tokenAmount: number;
    inBnb: number;
    inBnbWithFee: number;
  } | null>(null);

  // Sell calculation states
  const [poolTokenAmount, setPoolTokenAmount] = useState<string>("");
  const [sellTokenAmount, setSellTokenAmount] = useState<string>("");
  const [sellK, setSellK] = useState<string>("0.0000000163963");
  const [sellResults, setSellResults] = useState<{
    outBnb: number;
  } | null>(null);

  // 反向计算状态
  const [reverseBuyBnb, setReverseBuyBnb] = useState('');
  const [reverseBuyPoolBnb, setReverseBuyPoolBnb] = useState('');
  const [reverseBuyK, setReverseBuyK] = useState('0.0000000163963');
  const [reverseBuyResults, setReverseBuyResults] = useState<{
    tokenAmount: number;
    tokenAmountWithFee: number;
  } | null>(null);

  const calculateTokens = () => {
    const percentageValue = parseFloat(percentage);
    const poolBnbValue = parseFloat(poolBnb);
    const kValue = parseFloat(k);

    if (isNaN(percentageValue) || isNaN(poolBnbValue) || isNaN(kValue)) {
      alert("请输入有效的数值");
      return;
    }

    if (percentageValue <= 0 || percentageValue > 100) {
      alert("百分比必须在0-100之间");
      return;
    }

    // 计算目标token数量 (百分比 * 总供应量)
    const targetTokenAmount = (percentageValue / 100) * totalSupply;

    // 根据公式1反推InBnb: OutTokenAmount = 2/k * (sqrt(PoolBnb + InBnb) - sqrt(PoolBnb))
    // 重新整理公式: InBnb = (OutTokenAmount * k / 2 + sqrt(PoolBnb))^2 - PoolBnb
    const term = (targetTokenAmount * kValue) / 2 + Math.sqrt(poolBnbValue);
    const inBnbValue = Math.pow(term, 2) - poolBnbValue;

    // Add 3% transaction fee consideration
    const inBnbWithFee = inBnbValue / 0.97;

    setBuyResults({
      tokenAmount: targetTokenAmount,
      inBnb: inBnbValue,
      inBnbWithFee: inBnbWithFee
    });
  };

  const calculateSellBnb = () => {
    const poolTokenValue = parseFloat(poolTokenAmount);
    const sellTokenValue = parseFloat(sellTokenAmount);
    const kValue = parseFloat(sellK);

    if (isNaN(poolTokenValue) || isNaN(sellTokenValue) || isNaN(kValue)) {
      alert("请输入有效的数值");
      return;
    }

    if (sellTokenValue <= 0 || poolTokenValue <= 0) {
      alert("代币数量必须大于0");
      return;
    }

    // 公式2: OutBnbAmount = k^2/4 * (2 * PoolToken * SellToken - SellToken^2)
    const outBnbAmount = (Math.pow(kValue, 2) / 4) * (2 * poolTokenValue * sellTokenValue - Math.pow(sellTokenValue, 2));

    setSellResults({
      outBnb: outBnbAmount
    });
  };

  // 反向计算：根据投入的BNB计算可买到的token数量
  const calculateReverseBuy = () => {
    const buyBnbValue = parseFloat(reverseBuyBnb);
    const poolBnbValue = parseFloat(reverseBuyPoolBnb);
    const kValue = parseFloat(reverseBuyK);

    if (isNaN(buyBnbValue) || isNaN(poolBnbValue) || isNaN(kValue)) {
      alert('请输入有效的数值');
      return;
    }

    // 考虑3%手续费，实际用于购买的BNB = 输入的BNB * 0.97
    const actualBuyBnb = buyBnbValue * 0.97;

    // 使用公式1的反向计算: OutTokenAmount = (2/k) × (√(PoolBnb + InBnb) - √(PoolBnb))
    const tokenAmount = (2 / kValue) * (Math.sqrt(poolBnbValue + actualBuyBnb) - Math.sqrt(poolBnbValue));

    // 不考虑手续费的token数量
    const tokenAmountWithoutFee = (2 / kValue) * (Math.sqrt(poolBnbValue + buyBnbValue) - Math.sqrt(poolBnbValue));

    setReverseBuyResults({
      tokenAmount: tokenAmount,
      tokenAmountWithFee: tokenAmountWithoutFee
    });
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl relative z-10">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">代币经济学计算器</h1>
        <p className="text-muted-foreground">IP coin 买卖计算工具</p>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* 买入计算 */}
        <Card>
          <CardHeader>
            <CardTitle>买入计算 (公式1)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="percentage">代币百分比 (%)</Label>
              <Input
                id="percentage"
                type="number"
                placeholder="例如: 10 (代表10%)"
                value={percentage}
                onChange={(e) => setPercentage(e.target.value)}
                min="0"
                max="100"
                step="0.01"
              />
              <p className="text-sm text-muted-foreground">
                总供应量: {totalSupply.toLocaleString()} 代币
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="poolBnb">池中BNB数量</Label>
              <Input
                id="poolBnb"
                type="number"
                placeholder="例如: 0.06"
                value={poolBnb}
                onChange={(e) => setPoolBnb(e.target.value)}
                step="0.000001"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="k">参数 k</Label>
              <Input
                id="k"
                type="number"
                placeholder="例如: 0.0000000163963"
                value={k}
                onChange={(e) => setK(e.target.value)}
                step="0.00000001"
              />
            </div>

            <Button onClick={calculateTokens} className="w-full">
              计算买入
            </Button>

            {/* 买入结果 */}
            {buyResults && (
              <div className="mt-4 space-y-3 border-t pt-4">
                <h4 className="font-semibold">买入结果</h4>
                <div className="space-y-2">
                  <Label>代币数量</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="font-mono text-lg">
                      {buyResults.tokenAmount.toLocaleString()} 代币
                    </p>
                    <p className="text-sm text-muted-foreground">
                      占总供应量的 {percentage}%
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>需要投入的BNB (不含手续费)</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="font-mono text-lg">
                      {buyResults.inBnb.toFixed(8)} BNB
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>实际需要投入的BNB (含3%手续费)</Label>
                  <div className="p-3 bg-muted rounded-md border-2 border-primary">
                    <p className="font-mono text-lg font-semibold">
                      {buyResults.inBnbWithFee.toFixed(8)} BNB
                    </p>
                    <p className="text-sm text-muted-foreground">
                      包含3%交易手续费
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 卖出计算 */}
        <Card>
          <CardHeader>
            <CardTitle>卖出计算 (公式2)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="poolTokenAmount">池中代币数量</Label>
              <Input
                id="poolTokenAmount"
                type="number"
                placeholder="例如: 100000000"
                value={poolTokenAmount}
                onChange={(e) => setPoolTokenAmount(e.target.value)}
                step="1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sellTokenAmount">要卖出的代币数量</Label>
              <Input
                id="sellTokenAmount"
                type="number"
                placeholder="例如: 50000000"
                value={sellTokenAmount}
                onChange={(e) => setSellTokenAmount(e.target.value)}
                step="1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sellK">参数 k</Label>
              <Input
                id="sellK"
                type="number"
                placeholder="例如: 0.0000000163963"
                value={sellK}
                onChange={(e) => setSellK(e.target.value)}
                step="0.00000001"
              />
            </div>

            <Button onClick={calculateSellBnb} className="w-full">
              计算卖出
            </Button>

            {/* 卖出结果 */}
            {sellResults && (
              <div className="mt-4 space-y-3 border-t pt-4">
                <h4 className="font-semibold">卖出结果</h4>
                <div className="space-y-2">
                  <Label>可获得BNB</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="font-mono text-lg">
                      {sellResults.outBnb.toFixed(8)} BNB
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 反向买入计算 */}
        <Card>
          <CardHeader>
            <CardTitle>反向买入计算 (公式1)</CardTitle>
            <p className="text-sm text-muted-foreground">根据投入的BNB数量计算可买到的代币数量</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="reverseBuyBnb">投入的BNB数量</Label>
              <Input
                id="reverseBuyBnb"
                type="number"
                placeholder="例如: 1.0"
                value={reverseBuyBnb}
                onChange={(e) => setReverseBuyBnb(e.target.value)}
                step="0.000001"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reverseBuyPoolBnb">池中BNB数量</Label>
              <Input
                id="reverseBuyPoolBnb"
                type="number"
                placeholder="例如: 0.06"
                value={reverseBuyPoolBnb}
                onChange={(e) => setReverseBuyPoolBnb(e.target.value)}
                step="0.000001"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reverseBuyK">参数 k</Label>
              <Input
                id="reverseBuyK"
                type="number"
                placeholder="例如: 0.0000000163963"
                value={reverseBuyK}
                onChange={(e) => setReverseBuyK(e.target.value)}
                step="0.00000001"
              />
            </div>

            <Button onClick={calculateReverseBuy} className="w-full">
              计算可买代币数量
            </Button>

            {/* 反向买入结果 */}
            {reverseBuyResults && (
              <div className="mt-4 space-y-3 border-t pt-4">
                <h4 className="font-semibold">计算结果</h4>
                <div className="space-y-2">
                  <Label>实际可买到的代币数量 (扣除3%手续费)</Label>
                  <div className="p-3 bg-muted rounded-md border-2 border-primary">
                    <p className="font-mono text-lg font-semibold">
                      {reverseBuyResults.tokenAmount.toLocaleString()} 代币
                    </p>
                    <p className="text-sm text-muted-foreground">
                      已扣除3%交易手续费
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>理论代币数量 (不含手续费)</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="font-mono text-lg">
                      {reverseBuyResults.tokenAmountWithFee.toLocaleString()} 代币
                    </p>
                    <p className="text-sm text-muted-foreground">
                      不考虑手续费的理论值
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 公式说明 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>公式说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">公式1: 使用BNB买入Token</h4>
            <div className="bg-muted p-3 rounded-md font-mono text-sm">
              OutTokenAmount = (2/k) × (√(PoolBnb + InBnb) - √(PoolBnb))
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">公式2: 卖出Token兑换BNB</h4>
            <div className="bg-muted p-3 rounded-md font-mono text-sm">
              OutBnbAmount = (k²/4) × (2 × PoolToken × SellToken - SellToken²)
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            <p>• 总供应量固定为 1,000,000,000 代币</p>
            <p>• 输入百分比来计算对应的代币数量</p>
            <p>• 系统会自动计算需要投入的BNB数量</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default App;
