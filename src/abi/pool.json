[{"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "UNISWAP_ROUTER", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "aiWallet", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "attacker", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "bigBuyAmount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "coin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IPCoin"}], "stateMutability": "view"}, {"type": "function", "name": "creator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "creatorNft", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract CreatorNft"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "feeReceiver", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "nft", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract TraderNft"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pool", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract DistributionPool"}], "stateMutability": "view"}, {"type": "function", "name": "poolProxy", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testAddAdmin", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testAiWalletAndCreatorNft", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testAutoLiquidityPoolCreation", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testBatchRewardDistribution", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testBuy", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testBuyAfterListing", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testContinuousBuySell", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testCreatorRewardDistribution", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testCustomIntervalDistribution", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testDexTrading", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testFeeCalculationAccuracy", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGlobalIntervalFallback", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLargeTradeSlippage", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testMaxTokenCalculation", "inputs": [], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "testMinimumBuyAmount", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testMultipleTradesFeeAccuracy", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testPause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testPriceManipulationResistance", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testRemoveAdmin", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testRewardDistributor<PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testSell", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testSellAfterListing", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testSetGlobalRewardInterval", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testSetMaxWalletDistributionPercentage", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testSetRewardInterval", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testSmoothInitialPricing", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testVersionControl", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testWeeklyRewardLimit", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testWithdrawFee", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_RevertWhen_BuyingWithInsufficientEth", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "user1", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "user2", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}]