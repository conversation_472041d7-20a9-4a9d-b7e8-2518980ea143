export const POOL_ABI = [
    {
        "type": "receive",
        "stateMutability": "payable"
    },
    {
        "type": "function",
        "name": "IS_TEST",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "bool",
                "internalType": "bool"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "UNISWAP_ROUTER",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "address"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "aiWallet",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "address"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "attacker",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "address"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "bigBuyAmount",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "uint256",
                "internalType": "uint256"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "coin",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "contract IPCoin"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "creator",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "address"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "creatorNft",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "contract CreatorNft"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "excludeArtifacts",
        "inputs": [],
        "outputs": [
            {
                "name": "excludedArtifacts_",
                "type": "string[]",
                "internalType": "string[]"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "excludeContracts",
        "inputs": [],
        "outputs": [
            {
                "name": "excludedContracts_",
                "type": "address[]",
                "internalType": "address[]"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "excludeSelectors",
        "inputs": [],
        "outputs": [
            {
                "name": "excludedSelectors_",
                "type": "tuple[]",
                "internalType": "struct StdInvariant.FuzzSelector[]",
                "components": [
                    {
                        "name": "addr",
                        "type": "address",
                        "internalType": "address"
                    },
                    {
                        "name": "selectors",
                        "type": "bytes4[]",
                        "internalType": "bytes4[]"
                    }
                ]
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "excludeSenders",
        "inputs": [],
        "outputs": [
            {
                "name": "excludedSenders_",
                "type": "address[]",
                "internalType": "address[]"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "failed",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "bool",
                "internalType": "bool"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "feeReceiver",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "address"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "nft",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "contract TraderNft"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "owner",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "address"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "pool",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "contract DistributionPool"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "poolProxy",
        "inputs": [],
        "outputs": [
            {
                "name": "",
                "type": "address",
                "internalType": "address"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "setUp",
        "inputs": [],
        "outputs": [],
        "stateMutability": "nonpayable"
    },
    {
        "type": "function",
        "name": "targetArtifactSelectors",
        "inputs": [],
        "outputs": [
            {
                "name": "targetedArtifactSelectors_",
                "type": "tuple[]",
                "internalType": "struct StdInvariant.FuzzArtifactSelector[]",
                "components": [
                    {
                        "name": "artifact",
                        "type": "string",
                        "internalType": "string"
                    },
                    {
                        "name": "selectors",
                        "type": "bytes4[]",
                        "internalType": "bytes4[]"
                    }
                ]
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "targetArtifacts",
        "inputs": [],
        "outputs": [
            {
                "name": "targetedArtifacts_",
                "type": "string[]",
                "internalType": "string[]"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "targetContracts",
        "inputs": [],
        "outputs": [
            {
                "name": "targetedContracts_",
                "type": "address[]",
                "internalType": "address[]"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "targetInterfaces",
        "inputs": [],
        "outputs": [
            {
                "name": "targetedInterfaces_",
                "type": "tuple[]",
                "internalType": "struct StdInvariant.FuzzInterface[]",
                "components": [
                    {
                        "name": "addr",
                        "type": "address",
                        "internalType": "address"
                    },
                    {
                        "name": "artifacts",
                        "type": "string[]",
                        "internalType": "string[]"
                    }
                ]
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "targetSelectors",
        "inputs": [],
        "outputs": [
            {
                "name": "targetedSelectors_",
                "type": "tuple[]",
                "internalType": "struct StdInvariant.FuzzSelector[]",
                "components": [
                    {
                        "name": "addr",
                        "type": "address",
                        "internalType": "address"
                    },
                    {
                        "name": "selectors",
                        "type": "bytes4[]",
                        "internalType": "bytes4[]"
                    }
                ]
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "targetSenders",
        "inputs": [],
        "outputs": [
            {
                "name": "targetedSenders_",
                "type": "address[]",
                "internalType": "address[]"
            }
        ],
        "stateMutability": "view"
    },
    {
        "type": "function",
        "name": "testAddAdmin",
        "inputs": [],
        "outputs": [],
        "stateMutability": "nonpayable"
    },
    {
        "type": "function",
        "name": "testAddPauser",
        "inputs": [],
        "outputs": [],
        "stateMutability": "nonpayable"
    },
    {
        "type": "function",
        "name": "testAiWalletAndCreatorNft",
        "inputs": [],
        "outputs": [],
        "stateMutability": "nonpayable"
    },
    {
        "type": "function",
        "name": "testAutoLiquidityPoolCreation",
        "inputs": [],
        "outputs": [],
        "stateMutability": "nonpayable"
    }
];
